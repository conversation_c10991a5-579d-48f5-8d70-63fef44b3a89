#!/bin/bash

# ChatGLM RestTemplate SSE 测试脚本
# 用于测试基于 RestTemplate 的流式响应实现

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
BASE_URL="http://localhost:8080/api/v1/chat"

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务器是否运行
check_server() {
    print_info "检查服务器状态..."
    
    if curl -s --connect-timeout 5 "$BASE_URL/test/chatglm/health" > /dev/null; then
        print_success "服务器运行正常"
        return 0
    else
        print_error "服务器未运行或无法连接"
        print_info "请确保应用程序在 http://localhost:8080 上运行"
        return 1
    fi
}

# 测试 ChatGLM 连接
test_chatglm_connection() {
    print_info "测试 ChatGLM API 连接..."
    
    response=$(curl -s "$BASE_URL/test/chatglm/health")
    echo "响应: $response"
    
    if echo "$response" | grep -q '"status":"healthy"'; then
        print_success "ChatGLM API 连接正常"
        return 0
    else
        print_warning "ChatGLM API 连接可能有问题"
        return 1
    fi
}

# 测试 RestTemplate SSE 流式响应
test_resttemplate_sse_stream() {
    print_info "测试 ChatGLM RestTemplate SSE 流式响应..."
    print_info "发送消息: '你好，请简单介绍一下你自己'"
    
    echo
    print_info "=== RestTemplate SSE 响应开始 ==="
    
    # 使用 timeout 命令限制测试时间为 60 秒
    timeout 60s curl -X POST "$BASE_URL/test/chatglm/resttemplate" \
        -H "Content-Type: application/json" \
        -H "Accept: text/event-stream" \
        -d '{
              "model": "glm-4-air",
              "messages": [
                {
                  "role": "user",
                  "content": "你好，请简单介绍一下你自己"
                }
              ],
              "stream": true,
              "temperature": 0.1
            }' \
        --no-buffer \
        -w "\n"
    
    local exit_code=$?
    echo
    print_info "=== RestTemplate SSE 响应结束 ==="
    
    if [ $exit_code -eq 0 ]; then
        print_success "RestTemplate SSE 流式响应测试完成"
    elif [ $exit_code -eq 124 ]; then
        print_warning "RestTemplate SSE 测试超时（60秒），但这可能是正常的"
    else
        print_error "RestTemplate SSE 测试失败，退出码: $exit_code"
    fi
}

# 对比测试：原始 OkHttp 实现
test_okhttp_sse_stream() {
    print_info "对比测试：原始 OkHttp SSE 流式响应..."
    print_info "发送消息: '你好，请简单介绍一下你自己'"
    
    echo
    print_info "=== OkHttp SSE 响应开始 ==="
    
    # 使用 timeout 命令限制测试时间为 60 秒
    timeout 60s curl -X POST "$BASE_URL/test/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Accept: text/event-stream" \
        -d '{
              "model": "glm-4-air",
              "messages": [
                {
                  "role": "user",
                  "content": "你好，请简单介绍一下你自己"
                }
              ],
              "stream": true,
              "temperature": 0.1
            }' \
        --no-buffer \
        -w "\n"
    
    local exit_code=$?
    echo
    print_info "=== OkHttp SSE 响应结束 ==="
    
    if [ $exit_code -eq 0 ]; then
        print_success "OkHttp SSE 流式响应测试完成"
    elif [ $exit_code -eq 124 ]; then
        print_warning "OkHttp SSE 测试超时（60秒），但这可能是正常的"
    else
        print_error "OkHttp SSE 测试失败，退出码: $exit_code"
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "ChatGLM RestTemplate SSE 测试工具"
    echo "=========================================="
    echo
    
    # 检查服务器
    if ! check_server; then
        exit 1
    fi
    
    echo
    
    # 测试 ChatGLM 连接
    test_chatglm_connection
    
    echo
    
    # 测试 RestTemplate 实现
    print_info "开始 RestTemplate 实现测试..."
    test_resttemplate_sse_stream
    
    echo
    echo "=========================================="
    
    # 对比测试 OkHttp 实现
    print_info "开始对比测试（OkHttp 实现）..."
    test_okhttp_sse_stream
    
    echo
    echo "=========================================="
    print_info "测试完成！"
    print_info "请对比两种实现的响应效果"
    echo "=========================================="
}

# 运行主函数
main "$@"
