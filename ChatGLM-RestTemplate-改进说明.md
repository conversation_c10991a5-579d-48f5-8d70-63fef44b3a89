# ChatGLM Service RestTemplate 改进说明

## 问题分析

原始的 ChatGLMService 使用 OkHttp 的 EventSource 来处理 SSE 流，但存在"老是收不到消息"的问题。经过分析，可能的原因包括：

1. **EventSource 兼容性问题**：OkHttp 的 EventSource 对某些 API 的 SSE 格式兼容性不够好
2. **事件解析问题**：自动解析 SSE 事件可能跳过了某些重要数据
3. **连接管理问题**：连接可能被过早关闭或超时

## 解决方案

参考您提供的代码，我实现了基于 RestTemplate 的新版本，主要改进包括：

### 1. 新增 RestTemplate 配置

创建了 `RestTemplateConfig` 类：
- 设置合适的超时时间（连接30秒，读取300秒）
- 关闭请求体缓冲以支持流式处理
- 配置专门用于流式响应的 HTTP 客户端

### 2. 新增 RestTemplate 实现方法

在 `ChatGlmService` 中添加了两个新方法：

#### `chatStreamWithRestTemplate()`
- 直接返回 SseEmitter 的同步方法
- 使用 ExecutorService 进行异步处理
- 参考您提供的代码模式

#### `handleStreamRequestWithRestTemplate()`
- 异步方法，配合 Controller 使用
- 更好的错误处理和超时管理
- 支持 ChatCompletionRequest 对象

### 3. 关键技术改进

#### 手动请求处理
```java
restTemplate.execute(
    chatUrl,
    HttpMethod.POST,
    request -> {
        // 关键修复：手动设置Content-Type和请求头
        request.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        request.getHeaders().addAll(requestEntity.getHeaders());
        
        // 写入请求体
        if (requestEntity.getBody() != null) {
            new ObjectMapper().writeValue(request.getBody(), requestEntity.getBody());
        }
    },
    response -> {
        // 手动处理响应流
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getBody()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("event: ping")) {
                    continue; // 跳过心跳事件
                }
                emitter.send(line);
            }
        }
        emitter.complete();
        return null;
    }
);
```

#### 流式数据处理
- 使用 `BufferedReader` 逐行读取响应
- 跳过心跳事件（`event: ping`）
- 直接转发所有数据行到客户端
- 正确处理 SSE 格式的数据

### 4. 新增测试端点

在 `ChatController` 中添加了新的测试端点：
- `/api/v1/chat/test/chatglm/resttemplate` - 使用 RestTemplate 实现
- 保留原有的 `/api/v1/chat/test/chat/completions` - 使用 OkHttp 实现

## 使用方法

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 测试 RestTemplate 实现
```bash
./test-resttemplate-sse.sh
```

### 3. 手动测试
```bash
curl -X POST "http://localhost:8080/api/v1/chat/test/chatglm/resttemplate" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "model": "glm-4-air",
    "messages": [
      {
        "role": "user",
        "content": "你好"
      }
    ],
    "stream": true,
    "temperature": 0.1
  }' \
  --no-buffer
```

## 对比测试

测试脚本会同时测试两种实现：
1. **RestTemplate 实现**：`/test/chatglm/resttemplate`
2. **OkHttp 实现**：`/test/chat/completions`

您可以对比两种实现的响应效果，看哪种更稳定。

## 配置说明

### 超时时间
- **连接超时**：30秒
- **读取超时**：300秒（5分钟）
- **SseEmitter 超时**：300秒

### 关键配置
```java
// RestTemplate 配置
factory.setBufferRequestBody(false); // 关键：不缓冲请求体

// SseEmitter 配置
SseEmitter emitter = new SseEmitter(300_000L); // 5分钟超时
```

## 预期效果

使用 RestTemplate 实现后，应该能够：
1. **稳定接收消息**：不再出现"收不到消息"的问题
2. **更好的兼容性**：手动处理 SSE 格式，兼容性更好
3. **更详细的日志**：可以看到每一行接收到的数据
4. **更好的错误处理**：明确的异常信息和超时处理

## 注意事项

1. **保持向后兼容**：原有的 OkHttp 实现仍然保留
2. **性能考虑**：RestTemplate 实现可能比 EventSource 稍慢，但更稳定
3. **内存使用**：手动处理流可能使用更多内存，但在可接受范围内

如果 RestTemplate 实现效果更好，可以考虑将其设为默认实现。
