package cn.com.chinastock.cnf.playbook.service;

import cn.com.chinastock.cnf.playbook.model.openai.ChatMessage;
import com.alibaba.fastjson2.JSON;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ChatGLM 测试服务
 * 用于验证 SSE 流式响应功能
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@Service
public class ChatGlmService {

    private static final Logger logger = LoggerFactory.getLogger(ChatGlmService.class);
    
    // ChatGLM API 配置
    private static final String CHATGLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4";
    private static final String CHATGLM_MODEL = "glm-4-air";
    private static final String CHATGLM_API_KEY = "850ee857ad5103d591cfed9284c01c8a.0Bsn7Lku1KzGeKYj";
    
    private final OkHttpClient httpClient;

    public ChatGlmService() {
        // 初始化 HTTP 客户端，增加超时时间用于调试
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)  // 增加读取超时时间
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        logger.info("ChatGLM test service initialized");
    }
    
    /**
     * 发起 ChatGLM 聊天请求（流式）
     */
    public void chatStream(List<ChatMessage> messages, SseEmitter emitter) {
        try {
            logger.info("Starting ChatGLM stream request with {} messages", messages.size());
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("model", CHATGLM_MODEL);
            requestData.put("messages", messages);
            requestData.put("stream", true);
            requestData.put("temperature", 0.1);
            
            String requestJson = JSON.toJSONString(requestData);
            logger.debug("ChatGLM request: {}", requestJson);
            
            Request request = new Request.Builder()
                    .url(CHATGLM_BASE_URL + "/chat/completions")
                    .header("Authorization", "Bearer " + CHATGLM_API_KEY)
                    .header("Accept", "text/event-stream")
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(
                            requestJson,
                            okhttp3.MediaType.get("application/json")
                    ))
                    .build();
            
            logger.info("Sending request to ChatGLM API...");

            EventSources.createFactory(httpClient).newEventSource(request, new EventSourceListener() {
                @Override
                public void onOpen(EventSource eventSource, Response response) {
                    logger.info("ChatGLM SSE connection opened, response code: {}", response.code());
                    if (!response.isSuccessful()) {
                        logger.error("ChatGLM API returned error: {} {}", response.code(), response.message());
                        try {
                            String errorBody = response.body() != null ? response.body().string() : "No error body";
                            logger.error("Error response body: {}", errorBody);
                        } catch (Exception e) {
                            logger.error("Failed to read error response body", e);
                        }
                        emitter.completeWithError(new RuntimeException("ChatGLM API error: " + response.code()));
                    }
                }

                @Override
                public void onEvent(EventSource eventSource, String id, String type, String data) {
                    try {
                        logger.debug("Received SSE event - id: {}, type: {}, data: {}", id, type, data);

                        if ("[DONE]".equals(data)) {
                            logger.info("ChatGLM stream completed");
                            emitter.complete();
                            return;
                        }

                        if (data != null && !data.trim().isEmpty()) {
                            // 直接转发 ChatGLM 的响应，因为它已经是 OpenAI 兼容格式
                            // 使用正确的 SSE 格式：data: + 内容 + \n\n
                            emitter.send(SseEmitter.event().data(data));
                            logger.debug("Forwarded data to client: {}", data);
                        }
                    } catch (Exception e) {
                        logger.error("Error processing ChatGLM SSE event", e);
                        emitter.completeWithError(e);
                    }
                }

                @Override
                public void onClosed(EventSource eventSource) {
                    logger.info("ChatGLM SSE connection closed");
                    try {
                        emitter.complete();
                    } catch (Exception e) {
                        logger.warn("Error completing emitter on close", e);
                    }
                }

                @Override
                public void onFailure(EventSource eventSource, Throwable t, Response response) {
                    logger.error("ChatGLM SSE connection failed", t);
                    if (response != null) {
                        logger.error("Response code: {}, message: {}", response.code(), response.message());
                        try {
                            String errorBody = response.body() != null ? response.body().string() : "No error body";
                            logger.error("Error response body: {}", errorBody);
                        } catch (Exception e) {
                            logger.error("Failed to read error response body", e);
                        }
                    }
                    emitter.completeWithError(t);
                }
            });

        } catch (Exception e) {
            logger.error("Error setting up ChatGLM stream request", e);
            emitter.completeWithError(e);
        }
    }
}
